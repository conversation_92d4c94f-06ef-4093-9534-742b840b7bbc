import { EventEmitter } from 'events';

export interface WorkerMessage {
  type: string;
  payload: any;
}

export class WorkerManager {
  private workers: Map<string, Worker>;
  private eventEmitter: EventEmitter;

  constructor() {
    this.workers = new Map();
    this.eventEmitter = new EventEmitter();
  }

  // Создание нового воркера
  createWorker(name: string, script: string): Worker {
    if (this.workers.has(name)) {
      throw new Error(`Worker ${name} already exists`);
    }

    const worker = new Worker(script);
    this.workers.set(name, worker);

    worker.onmessage = (event: MessageEvent) => {
      this.eventEmitter.emit(`${name}:message`, event.data);
    };

    worker.onerror = (error: ErrorEvent) => {
      this.eventEmitter.emit(`${name}:error`, error);
    };

    return worker;
  }

  // Отправка сообщения воркеру
  postMessage(name: string, message: WorkerMessage): void {
    const worker = this.workers.get(name);
    if (!worker) {
      throw new Error(`Worker ${name} not found`);
    }
    worker.postMessage(message);
  }

  // Подписка на сообщения от воркера
  onMessage(name: string, callback: (data: any) => void): void {
    this.eventEmitter.on(`${name}:message`, callback);
  }

  // Подписка на ошибки воркера
  onError(name: string, callback: (error: ErrorEvent) => void): void {
    this.eventEmitter.on(`${name}:error`, callback);
  }

  // Завершение работы воркера
  terminate(name: string): void {
    const worker = this.workers.get(name);
    if (worker) {
      worker.terminate();
      this.workers.delete(name);
      this.eventEmitter.removeAllListeners(`${name}:message`);
      this.eventEmitter.removeAllListeners(`${name}:error`);
    }
  }

  // Завершение всех воркеров
  terminateAll(): void {
    this.workers.forEach((worker, name) => {
      this.terminate(name);
    });
  }
}

// Создание синглтона
export const workerManager = new WorkerManager(); 